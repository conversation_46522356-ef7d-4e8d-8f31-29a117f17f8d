<template>
  <el-dialog
    title="批量导入工单"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleBatchCancel"
  >
    <el-form :model="batchForm" ref="batchForm" label-width="140px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="下载模版:" prop="file">
            <el-link type="primary" @click="openTemplateModal"
              >点击下载</el-link
            >
          </el-form-item></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传文件:" prop="file">
            <el-upload
              ref="upload"
              :limit="1"
              accept=".xlsx, .xls"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :on-change="handleChangeFile"
              :auto-upload="false"
              :data="extraData"
            >
              <el-button>选择文件</el-button>
              <div slot="tip" class="el-upload__tip">
                上传格式支持xlxs、xls文件，500m以内。
              </div>
            </el-upload>
          </el-form-item></el-col
        >
      </el-row>
      <slot name="extraForm" :params="extraData"></slot>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        @click="handleBatchCancel"
        size="small"
        :loading="submitLoading"
        >取 消
      </el-button>
      <el-button
        type="primary"
        @click="handleBatchSubmit"
        size="small"
        :loading="submitLoading"
        >保存</el-button
      >
    </div>

    <!-- 模板下载弹窗 -->
    <BaseFormModal
      ref="templateModal"
      :modalTitle="templateModalConfig.modalTitle"
      :config="templateModalConfig.formConfig"
      @modalConfirm="handleTemplateModalConfirm"
      modalWidth="50%"
      labelWidth="140px"
      :autoClose="false"
    >
      <template #templateType="{ item, params }">
        <el-radio-group
          v-model="params[item.field]"
          @change="handleTemplateTypeChange"
        >
          <el-radio
            v-for="option in item.props.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </template>

      <!-- 下载模板按钮 -->
      <template #modalFooter>
        <div
          style="display: flex; justify-content: space-between; width: 100%;"
        >
          <el-button
            type="primary"
            @click="handleDirectDownload"
            :loading="downloadLoading"
            icon="el-icon-download"
          >
            下载模板
          </el-button>
          <div>
            <el-button @click="$refs.templateModal.closeVisible()"
              >取 消</el-button
            >
            <el-button
              type="primary"
              @click="handleTemplateModalConfirm"
              :loading="submitLoading"
            >
              保 存
            </el-button>
          </div>
        </div>
      </template>
    </BaseFormModal>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";

import { fileDownLoad } from "@/utils/downLoad.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import api from "@/api/ledger/index.js";
export default {
  components: { BaseFormModal },
  props: {
    //上传时附带的额外参数
    extraData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      visible: false,
      submitLoading: false,
      batchForm: {
        file: [],
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/ledger/order/import",
        updateAsCode: "",
      },
      // 工单类型和业务类型选项
      orderTypeOptions: [],
      // 模板弹窗表单数据
      templateFormData: {
        templateType: "normal",
      },
      // 下载按钮加载状态
      downloadLoading: false,
    };
  },
  computed: {
    templateModalConfig() {
      return {
        modalTitle: "批量导入工单",
        formConfig: [
          {
            field: "templateType",
            title: "模板类型",
            element: "slot",
            slotName: "templateType",
            props: {
              options: [
                { value: "normal", label: "常规模板" },
                { value: "byOrderType", label: "按工单类型下载模板" },
              ],
            },
            defaultValue: "normal",
            rules: [{ required: true, message: "请选择模板类型" }],
          },
          {
            field: "orderType",
            title: "工单类型",
            element: "el-cascader",
            show: this.templateFormData.templateType === "byOrderType",
            props: {
              options: this.orderTypeOptions,
              props: {
                checkStrictly: false,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              filterable: true,
            },
            rules: [
              {
                required: this.templateFormData.templateType === "byOrderType",
                message: "请选择工单类型",
              },
            ],
          },
          {
            field: "orderStatus",
            title: "数据处理方式",
            element: "el-radio-group",
            show: this.templateFormData.templateType === "normal",
            props: {
              options: [
                {
                  value: "1",
                  label: "处理中（开启流程，仅第一个节点默认已处理）",
                },
                {
                  value: "4",
                  label: "完成（批量完成所有工单，将所有节点变成已处理）",
                },
                {
                  value: "0",
                  label: "草稿（不开启流程，导入后工单状态为草稿）",
                },
              ],
            },
            defaultValue: "1",
            rules: [
              {
                required: this.templateFormData.templateType === "normal",
                message: "请选择数据处理方式",
              },
            ],
          },
          {
            field: "finishRemark",
            title: "完成原因",
            element: "el-input",
            show: this.templateFormData.templateType === "normal",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
      };
    },
  },
  watch: {
    // 监听模板弹窗表单数据变化，用于动态显示工单类型字段
    "templateFormData.templateType"() {
      this.$forceUpdate(); // 强制更新计算属性
    },
  },
  created() {
    this.loadOrderTypeOptions();
  },
  methods: {
    // 加载工单类型选项
    loadOrderTypeOptions() {
      queryDeptOrderTree({}).then((res) => {
        this.orderTypeOptions = res.data?.map((x) => ({ ...x }));
      });
    },
    // 打开模板下载弹窗
    openTemplateModal() {
      this.templateFormData = {
        templateType: "normal",
        orderType: [],
        orderStatus: "1",
        finishRemark: "",
      };
      this.$refs.templateModal.open(this.templateFormData);
    },
    // 处理模板类型变化
    handleTemplateTypeChange(value) {
      this.templateFormData.templateType = value;
      // 清空工单类型选择
      if (value === "normal") {
        this.$refs.templateModal.setFormFields({
          orderType: undefined,
          orderStatus: "1",
          finishRemark: "",
        });
      } else {
        this.$refs.templateModal.setFormFields({
          orderType: [],
          orderStatus: undefined,
          finishRemark: undefined,
        });
      }
    },

    // 直接下载模板
    async handleDirectDownload() {
      const formData = this.$refs.templateModal.getFormFields();
      this.downloadLoading = true;
      try {
        await this.handleTemplateDownload(formData);
      } finally {
        this.downloadLoading = false;
      }
    },

    // 模板弹窗确认按钮处理
    async handleTemplateModalConfirm() {
      const formData = this.$refs.templateModal.getFormFields();

      if (formData.templateType === "normal") {
        // 常规模板场景：调用原有的API接口
        this.handleBatchSubmitWithFormData(formData);
      } else {
        // 工单类型模板场景：调用新的API接口
        this.handleDynamicImport(formData);
      }
    },

    // 处理常规模板的批量提交
    handleBatchSubmitWithFormData(formData) {
      // 将表单数据设置到extraData中
      this.$emit("updateExtraData", {
        orderStatus: formData.orderStatus,
        finishRemark: formData.finishRemark,
      });

      // 关闭模板弹窗
      this.$refs.templateModal.closeVisible();

      // 提示用户上传文件
      this.$message.info("请上传文件后点击保存按钮进行导入");
    },

    // 处理动态导入
    async handleDynamicImport(formData) {
      try {
        // 设置动态导入标识和参数
        this.$emit("updateExtraData", {
          isDynamicImport: true,
          orderType: formData.orderType,
        });

        // 关闭模板弹窗
        this.$refs.templateModal.closeVisible();

        // 提示用户上传文件
        this.$message.info("请上传文件后点击保存按钮进行导入");
      } catch (error) {
        console.error("动态导入失败:", error);
        this.$message.error("动态导入失败，请稍后重试");
      }
    },
    // 处理模板下载
    async handleTemplateDownload(formData) {
      console.log("handleTemplateDownload", formData);
      try {
        if (formData.templateType === "normal") {
          // 下载常规模板
          this.downloadNormalTemplate();
          // this.$message.success("模板下载成功");
        } else {
          await this.downloadTemplateByOrderType(formData.orderType);
          // this.$message.success("模板下载成功");
        }
      } catch (error) {
        console.error("下载模板失败:", error);
        this.$message.error("下载模板失败，请稍后重试");
      }
    },
    // 下载常规模板
    downloadNormalTemplate() {
      window.location.href =
        "/charging-maintenance-ui/static/工单台账导入模板.xlsx";
    },
    // 按工单类型下载模板
    async downloadTemplateByOrderType(orderType) {
      try {
        const res = await api.downloadCustomTemplate({
          orderTypeList: orderType,
        });
        if (res) {
          await fileDownLoad(res);
          this.$refs.templateModal.closeVisible();
        }
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error("下载失败，请稍后重试");
        return false;
      }
    },
    open() {
      this.visible = true;
    },
    handleFileSuccess(response) {
      this.submitLoading = false;
      console.log("response===", response);
      if (!response.success) {
        this.$confirm(response.message, "导入失败！", {
          confirmButtonText: "重新上传",
          cancelButtonText: "取消",
          type: "error",
          center: true,
          dangerouslyUseHTMLString: true,
        })
          .then(() => {
            this.batchForm.file = [];
            this.$refs.upload.clearFiles();
          })
          .catch(() => {
            this.handleBatchCancel();
          });
      } else {
        this.handleBatchCancel();
        this.$alert("导入成功", "导入结果", {
          type: "success",
          confirmButtonText: "我知道了",
          callback: () => {
            this.$emit("uploadSuccess");
          },
        });
      }
    },
    handleFileError(response) {
      this.submitLoading = false;
      this.$confirm(response.message, "导入失败！", {
        confirmButtonText: "重新上传",
        cancelButtonText: "取消",
        type: "error",
        center: true,
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          this.batchForm.file = [];
          this.$refs.upload.clearFiles();
        })
        .catch(() => {
          this.handleBatchCancel();
        });
    },
    handleChangeFile(file, fileList) {
      console.log(file, fileList);
      this.batchForm.file = fileList || [];
    },
    //批量配置-提交
    handleBatchSubmit() {
      console.log("extraData", this.extraData);
      console.log(this.batchForm.file, "提交");
      if (this.batchForm.file?.length === 0) {
        this.$message.error("请上传文件！");
        return;
      }
      if (this.batchForm.file[0].size / 1024 / 1024 > 500) {
        this.$message.error("上传的文件大小不能超过500m!");
        return;
      }

      // 检查是否为动态导入模式
      if (this.extraData.isDynamicImport) {
        // 动态导入模式，调用新的API
        this.handleDynamicImportSubmit();
      } else {
        // 常规导入模式
        if (!this.extraData.orderStatus) {
          this.$message.error("请选择数据处理方式！");
          return;
        }
        this.submitLoading = true;
        this.$refs.upload.submit();
      }
    },

    // 处理动态导入提交
    async handleDynamicImportSubmit() {
      try {
        this.submitLoading = true;

        const formData = new FormData();
        formData.append("file", this.batchForm.file[0].raw);
        formData.append(
          "orderTypeList",
          JSON.stringify(this.extraData.orderType)
        );

        const response = await api.dynamicImport(formData);

        if (response.success) {
          this.handleBatchCancel();
          this.$alert("导入成功", "导入结果", {
            type: "success",
            confirmButtonText: "我知道了",
            callback: () => {
              this.$emit("uploadSuccess");
            },
          });
        } else {
          this.$confirm(response.message, "导入失败！", {
            confirmButtonText: "重新上传",
            cancelButtonText: "取消",
            type: "error",
            center: true,
            dangerouslyUseHTMLString: true,
          })
            .then(() => {
              this.batchForm.file = [];
              this.$refs.upload.clearFiles();
            })
            .catch(() => {
              this.handleBatchCancel();
            });
        }
      } catch (error) {
        console.error("动态导入失败:", error);
        this.$message.error("导入失败，请稍后重试");
      } finally {
        this.submitLoading = false;
      }
    },

    handleBatchCancel() {
      this.visible = false;
      this.$refs.batchForm.resetFields();
      this.batchForm.file = [];
      this.$refs.upload.clearFiles();
    },
  },
};
</script>

<style></style>
